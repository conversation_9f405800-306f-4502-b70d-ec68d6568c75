#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的采集逻辑
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from core.config_manager import ConfigManager
from utils.logger import setup_logger

def test_search_collection():
    """测试搜索采集功能"""
    print("🔍 开始测试搜索采集功能...")
    
    # 初始化
    config_manager = ConfigManager()
    collector = CollectorEngine(config_manager)
    
    # 进度回调函数
    def progress_callback(message, progress):
        print(f"进度: {progress}% - {message}")
    
    try:
        # 测试搜索"虚拟资料"
        keyword = "虚拟资料"
        max_pages = 2
        min_want_count = 5
        
        print(f"搜索关键词: {keyword}")
        print(f"最大页数: {max_pages}")
        print(f"最小想要人数: {min_want_count}")
        
        # 开始采集
        results = collector.collect_search_data(
            keyword=keyword,
            max_pages=max_pages,
            min_want_count=min_want_count,
            progress_callback=progress_callback
        )
        
        # 输出结果
        print(f"\n✅ 采集完成！共获取 {len(results)} 条数据")
        
        if results:
            print("\n📊 采集结果预览:")
            for i, item in enumerate(results[:5]):  # 只显示前5条
                print(f"\n商品 {i+1}:")
                print(f"  标题: {item.get('title', '未知')}")
                print(f"  价格: ¥{item.get('price', '未知')}")
                print(f"  想要人数: {item.get('wantCount', 0)}")
                print(f"  状态: {item.get('status', '未知')}")
                print(f"  链接: {item.get('link', '未知')[:50]}...")
                if item.get('image'):
                    print(f"  图片: {item.get('image')[:50]}...")
        
        return results
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []
    
    finally:
        # 清理资源
        if collector.page:
            collector.page.quit()

def test_shop_collection():
    """测试店铺采集功能"""
    print("\n🏪 开始测试店铺采集功能...")
    
    # 初始化
    config_manager = ConfigManager()
    collector = CollectorEngine(config_manager)
    
    # 进度回调函数
    def progress_callback(message, progress):
        print(f"进度: {progress}% - {message}")
    
    try:
        # 测试店铺URL（需要替换为实际的店铺URL）
        shop_url = "https://www.goofish.com/shop/123456"  # 示例URL
        max_items = 20
        min_want_count = 3
        
        print(f"店铺URL: {shop_url}")
        print(f"最大采集数量: {max_items}")
        print(f"最小想要人数: {min_want_count}")
        
        # 开始采集
        results = collector.collect_shop_data(
            shop_url=shop_url,
            max_items=max_items,
            min_want_count=min_want_count,
            progress_callback=progress_callback
        )
        
        # 输出结果
        print(f"\n✅ 店铺采集完成！共获取 {len(results)} 条数据")
        
        if results:
            print("\n📊 采集结果预览:")
            for i, item in enumerate(results[:3]):  # 只显示前3条
                print(f"\n商品 {i+1}:")
                print(f"  标题: {item.get('title', '未知')}")
                print(f"  价格: ¥{item.get('price', '未知')}")
                print(f"  想要人数: {item.get('wantCount', 0)}")
                print(f"  状态: {item.get('status', '未知')}")
        
        return results
        
    except Exception as e:
        print(f"❌ 店铺测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []
    
    finally:
        # 清理资源
        if collector.page:
            collector.page.quit()

def main():
    """主函数"""
    print("🚀 XY商品采集器 - 新版本测试")
    print("=" * 50)
    
    # 设置日志
    logger = setup_logger()
    
    try:
        # 测试搜索采集
        search_results = test_search_collection()
        
        # 如果搜索采集成功，可以选择测试店铺采集
        if search_results:
            print("\n" + "=" * 50)
            user_input = input("是否继续测试店铺采集功能？(y/n): ")
            if user_input.lower() == 'y':
                test_shop_collection()
        
        print("\n🎉 测试完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
