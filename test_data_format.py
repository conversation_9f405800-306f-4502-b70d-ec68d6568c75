#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据格式是否符合UI要求
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from core.config_manager import ConfigManager
from core.data_processor import DataProcessor, ProductDataModel
import logging

def test_data_format():
    """测试数据格式"""
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    logger.info("开始测试数据格式...")
    
    try:
        # 初始化组件
        config_manager = ConfigManager()
        engine = CollectorEngine(config_manager)
        data_processor = DataProcessor()
        
        # 测试采集功能
        logger.info("🔍 开始采集虚拟商品数据...")
        
        def progress_callback(progress_info):
            current_page = progress_info.get('current_page', 0)
            total_pages = progress_info.get('total_pages', 1)
            total_items = progress_info.get('total_items', 0)
            logger.info(f"📊 进度: 第{current_page}/{total_pages}页, 已采集{total_items}个商品")
        
        # 采集数据
        raw_products = engine.collect_search_data(
            keyword="虚拟商品",
            max_pages=1,  # 只测试1页
            min_want_count=1,
            progress_callback=progress_callback
        )
        
        if raw_products:
            logger.info(f"✅ 采集成功！获取到 {len(raw_products)} 个原始商品")
            
            # 数据清洗和验证
            cleaned_data = data_processor.clean_and_validate(raw_products)
            logger.info(f"🧹 数据清洗完成，有效数据 {len(cleaned_data)} 个")
            
            # 测试数据模型
            data_model = ProductDataModel(cleaned_data)
            logger.info(f"📋 数据模型创建成功，行数: {data_model.rowCount()}, 列数: {data_model.columnCount()}")
            
            # 显示前3个商品的详细信息
            logger.info("\n📝 商品数据格式验证:")
            for i in range(min(3, len(cleaned_data))):
                product = cleaned_data[i]
                logger.info(f"\n商品 {i+1}:")
                logger.info(f"  标题: {product.get('title', 'N/A')}")
                logger.info(f"  价格: {product.get('price', 0)} (类型: {type(product.get('price'))})")
                logger.info(f"  想要人数: {product.get('wantCount', 0)} (类型: {type(product.get('wantCount'))})")
                logger.info(f"  浏览次数: {product.get('browseCnt', 0)}")
                logger.info(f"  转化率: {product.get('conversionRate', 0)}")
                logger.info(f"  状态: {product.get('status', '在售')}")
                logger.info(f"  链接: {product.get('link', 'N/A')}")
                
                # 验证UI表格显示
                logger.info(f"\n  UI表格显示测试:")
                from PyQt5.QtCore import QModelIndex, Qt
                for col in range(data_model.columnCount()):
                    index = data_model.createIndex(i, col)
                    display_value = data_model.data(index, Qt.DisplayRole)
                    logger.info(f"    列{col}: {display_value}")
            
            logger.info(f"\n✅ 数据格式验证完成！")
            logger.info(f"📊 统计信息:")
            stats = data_processor.get_statistics(cleaned_data)
            for key, value in stats.items():
                logger.info(f"   {key}: {value}")
                
        else:
            logger.warning("⚠️ 未采集到任何商品数据")
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        try:
            if 'engine' in locals() and engine.page:
                engine.cleanup()
                logger.info("清理完成")
        except:
            pass

if __name__ == "__main__":
    test_data_format()
