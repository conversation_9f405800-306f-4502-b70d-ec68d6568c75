# [001] DrissionPage采集功能重构计划

**创建时间**: 2025-07-30T19:39:04+08:00  
**项目**: XY商品采集插件  
**目标**: 将DrissionPage MCP采集流程转换为纯Python代码

## 📋 任务目标

将经过验证的DrissionPage MCP采集流程转换为纯Python代码，实现：
1. 使用DrissionPage库打开浏览器搜索关键词
2. 滑动到页面底部加载所有商品
3. 提取商品信息：标题、价格、想要人数、浏览人数、转换率、状态、链接
4. 与现有UI界面无缝集成
5. 支持打包为exe独立运行

## 🎯 详细任务列表

### ✅ 任务1: 创建新的采集方法
- **描述**: 在collector_engine.py中创建基于DrissionPage的新采集方法
- **输入**: 搜索关键词、最大页数、最小想要人数
- **输出**: 标准化的商品数据列表
- **验收标准**: 
  - 能够成功打开浏览器并搜索
  - 正确滑动到页面底部
  - 提取完整的商品信息
  - 数据格式与现有ProductDataModel兼容

### ✅ 任务2: 实现JavaScript数据提取逻辑
- **描述**: 将MCP测试中验证的JavaScript代码转换为Python字符串
- **关键功能**:
  - 查找商品链接 `a[href*="item?id="]`
  - 提取商品ID、标题、价格、想要人数
  - 计算转换率和估算浏览人数
  - 获取商品状态和地区信息
- **验收标准**: JavaScript代码能正确提取所有必需字段

### ✅ 任务3: 集成到现有搜索流程
- **描述**: 修改main_window.py中的start_search_collection方法
- **修改点**:
  - 调用新的采集方法替代现有逻辑
  - 保持现有的进度回调和错误处理
  - 确保数据格式兼容
- **验收标准**: 点击搜索按钮能触发新的采集流程

### ✅ 任务4: 优化浏览器配置
- **描述**: 配置DrissionPage浏览器参数以适应打包环境
- **配置项**:
  - 无头模式支持
  - 用户代理设置
  - 窗口大小和参数
  - 错误处理机制
- **验收标准**: 在开发和打包环境中都能正常运行

### ✅ 任务5: 数据处理和验证
- **描述**: 确保提取的数据经过适当的清洗和验证
- **处理逻辑**:
  - 价格数据类型转换
  - 文本内容清理
  - 转换率计算验证
  - 重复数据去除
- **验收标准**: 数据质量符合现有标准

## 🔧 技术实现要点

### 核心采集流程
```python
def collect_with_drissionpage_mcp_style(self, keyword, max_pages=3, min_want_count=10):
    # 1. 初始化浏览器
    # 2. 导航到搜索页面
    # 3. 滑动到底部加载商品
    # 4. 执行JavaScript提取数据
    # 5. 数据清洗和格式化
    # 6. 返回标准化结果
```

### JavaScript提取逻辑
- 基于实际测试验证的选择器
- 容错处理机制
- 数据类型转换
- 地区信息提取

### 浏览器配置
- 使用ChromiumPage和ChromiumOptions
- 设置合适的用户代理
- 配置无头模式
- 优化性能参数

## 📊 进度跟踪

- [x] 任务1: 创建新的采集方法 ✅ **已完成** (2025-07-30T19:40:16+08:00)
- [x] 任务2: 实现JavaScript数据提取逻辑 ✅ **已完成** (2025-07-30T19:40:16+08:00)
- [x] 任务3: 集成到现有搜索流程 ✅ **已完成** (2025-07-30T19:40:16+08:00)
- [x] 任务4: 优化浏览器配置 ✅ **已完成** (2025-07-30T19:45:30+08:00)
- [x] 任务5: 数据处理和验证 ✅ **已完成** (2025-07-30T19:45:30+08:00)

## 🎉 完成标准

1. 点击搜索按钮能够触发新的采集流程
2. 成功提取商品的7个核心字段
3. 数据在UI界面正确显示
4. 代码可以打包为exe独立运行
5. 性能优于现有实现

**预计完成时间**: 2-3小时  
**风险等级**: 低  
**依赖项**: 无
