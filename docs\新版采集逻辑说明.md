# XY商品采集器 - 新版采集逻辑说明

## 概述

基于JavaScript版本的成功经验，我重新设计了Python版本的商品采集逻辑，采用更稳定和高效的方法。

## 主要改进

### 1. 数据提取逻辑优化

#### 多重选择器策略
```python
selectors = {
    'title': ['.main-title--sMrtWSJa', '.feeds-title--rGdH_KoF', '[class*="title"]', '.title'],
    'price': ['.price-wrap--YzmU5cUl', '[class*="price"]', '.price'],
    'want': ['.text--MaM9Cmdn[title*="人想要"]', '.feeds-want--rGdH_KoF', '[class*="want"]'],
    'image': ['.feeds-image--TDRC4fV1', 'img'],
    'link': ['a[href*="/item/"]', 'a']
}
```

#### 增强的数据验证
- 标题清理：去除特殊字符和多余空格
- 价格提取：支持多种价格格式
- 想要人数：支持多种文本格式（"123人想要"、"想要123"等）
- 图片URL：标准化处理，去除压缩参数

### 2. 翻页机制改进

#### 多种翻页方式
1. **点击下一页按钮**：尝试多种下一页按钮选择器
2. **点击具体页码**：直接点击目标页码
3. **输入框跳转**：使用页码输入框跳转

#### 智能页面变化检测
- 检查当前页码变化
- 检查商品列表更新
- 超时保护机制

### 3. 搜索采集流程

```python
def collect_search_data(self, keyword, max_pages, min_want_count, progress_callback):
    """
    搜索采集主流程：
    1. 初始化浏览器
    2. 导航到搜索页面
    3. 等待页面稳定
    4. 逐页采集数据
    5. 翻页处理
    6. 去重处理
    """
```

#### 关键步骤
1. **页面导航**：`https://www.goofish.com/search?q={keyword}`
2. **页面稳定检查**：等待商品列表容器加载
3. **商品提取**：滚动加载 + 逐个提取
4. **翻页处理**：多种方式尝试翻页
5. **数据去重**：基于商品链接去重

### 4. 店铺采集流程

```python
def collect_shop_data(self, shop_url, max_items, min_want_count, progress_callback):
    """
    店铺采集主流程：
    1. 导航到店铺页面
    2. 滚动加载所有商品
    3. 逐个提取商品数据
    4. 数量控制和过滤
    """
```

#### 关键特性
- **滚动加载**：自动滚动到底部加载所有商品
- **图片加载优化**：确保商品图片正确加载
- **数量控制**：支持限制采集数量
- **实时进度**：提供详细的采集进度反馈

### 5. 错误处理和稳定性

#### 异常处理
- 每个关键步骤都有try-catch保护
- 详细的错误日志记录
- 优雅的降级处理

#### 稳定性保证
- 页面加载等待机制
- 元素存在性检查
- 网络请求重试机制

## 核心方法说明

### `_extract_single_item_data(item, min_want_count)`
提取单个商品数据的核心方法，基于JS版本的逻辑：

```python
# 多重选择器尝试
for selector in selectors['title']:
    try:
        title_ele = item.ele(selector, timeout=0.5)
        if title_ele:
            title = title_ele.text.strip()
            break
    except:
        continue
```

### `_go_to_next_page(target_page)`
翻页处理方法，支持多种翻页方式：

```python
# 方法1：点击下一页按钮
if self._try_click_next_button():
    return self._wait_for_page_change(target_page)

# 方法2：点击具体页码
if self._try_click_page_number(target_page):
    return self._wait_for_page_change(target_page)

# 方法3：输入框跳转
if self._try_input_page_jump(target_page):
    return self._wait_for_page_change(target_page)
```

### `_wait_for_page_change(target_page, timeout)`
等待页面变化的智能检测：

```python
# 检查当前页码
current_page = self._get_current_page()
if current_page == target_page:
    return True

# 检查商品列表更新
items = self.page.eles('.feeds-item-wrap--rGdH_KoF')
if items and len(items) > 0:
    # 给页面更多时间稳定
    time.sleep(1)
    current_page = self._get_current_page()
    if current_page == target_page:
        return True
```

## 使用示例

### 搜索采集
```python
from core.collector_engine import CollectorEngine
from core.config_manager import ConfigManager

config_manager = ConfigManager()
collector = CollectorEngine(config_manager)

results = collector.collect_search_data(
    keyword="虚拟资料",
    max_pages=3,
    min_want_count=10,
    progress_callback=lambda msg, progress: print(f"{progress}% - {msg}")
)
```

### 店铺采集
```python
results = collector.collect_shop_data(
    shop_url="https://www.goofish.com/shop/123456",
    max_items=50,
    min_want_count=5,
    progress_callback=lambda msg, progress: print(f"{progress}% - {msg}")
)
```

## 测试验证

运行测试脚本验证新的采集逻辑：

```bash
python test_new_collector.py
```

测试内容：
1. 搜索"虚拟资料"关键词
2. 验证数据提取准确性
3. 检查翻页功能
4. 验证去重机制

## 性能优化

1. **减少等待时间**：优化页面加载等待逻辑
2. **智能重试**：失败时自动重试关键操作
3. **内存管理**：及时清理不需要的数据
4. **并发控制**：避免过快的请求频率

## 兼容性

- 支持DrissionPage 4.x版本
- 兼容最新的闲鱼网站结构
- 适配不同的页面布局变化

## 总结

新版采集逻辑基于JavaScript版本的成功经验，采用了更稳定的数据提取方法和更智能的翻页机制。通过多重选择器策略和增强的错误处理，大大提高了采集的成功率和数据质量。
