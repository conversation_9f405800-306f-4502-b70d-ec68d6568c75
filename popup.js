// 处理折叠面板
function setupCollapsible(headerId, contentId) {
  const header = document.getElementById(headerId);
  const content = document.getElementById(contentId);
  const arrow = header.querySelector('.arrow');
  
  header.addEventListener('click', () => {
    const isActive = content.classList.contains('active');
    
    // 切换内容区域
    content.classList.toggle('active');
    arrow.classList.toggle('active');
    header.classList.toggle('active');
    
    // 添加动画效果
    if (!isActive) {
      // 展开时的动画
      content.style.maxHeight = content.scrollHeight + 'px';
    } else {
      // 收起时的动画
      content.style.maxHeight = '0px';
    }
  });
}

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
  // 设置初始状态：搜索区域展开，店铺区域折叠
  const searchContent = document.getElementById('searchContent');
  const shopContent = document.getElementById('shopContent');
  const searchHeader = document.getElementById('searchHeader');
  const shopHeader = document.getElementById('shopHeader');
  const searchArrow = searchHeader.querySelector('.arrow');
  const shopArrow = shopHeader.querySelector('.arrow');

  // 初始状态：搜索区域展开，店铺区域折叠
  searchContent.classList.add('active');
  searchArrow.classList.add('active');
  searchHeader.classList.add('active');
  searchContent.style.maxHeight = searchContent.scrollHeight + 'px';
  
  // 设置折叠面板事件监听器
  setupCollapsible('searchHeader', 'searchContent');
  setupCollapsible('shopHeader', 'shopContent');

  // 搜索页面采集按钮
  document.getElementById('collectSearch').addEventListener('click', function() {
    sendMessageToActiveTab({
      action: 'collectSearch',
      minWantCount: parseInt(document.getElementById('minWantCount').value) || 0,
      pageCount: parseInt(document.getElementById('pageCount').value) || 1,
      enableDetailCollect: document.getElementById('detailCollectSearch').checked,
      detailInterval: parseInt(document.getElementById('detailIntervalSearch').value) || 3
    });
});

  // 停止采集按钮
  document.getElementById('stopCollect').addEventListener('click', function() {
    sendMessageToActiveTab({
      action: 'stopCollection'
    });
});

  // 搜索数据导出按钮
  document.getElementById('exportSearch').addEventListener('click', function() {
    sendMessageToActiveTab({
      action: 'exportSearch'
    });
});

  // 店铺采集按钮
  document.getElementById('collectShop').addEventListener('click', function() {
    sendMessageToActiveTab({
      action: 'collectShop',
      maxItems: parseInt(document.getElementById('shopMaxItems').value) || 0,
      minWantCount: parseInt(document.getElementById('shopMinWantCount').value) || 0,
      enableDetailCollect: document.getElementById('detailCollectShop').checked,
      detailInterval: parseInt(document.getElementById('detailIntervalShop').value) || 3
    });
});

  // 店铺数据导出按钮
  document.getElementById('exportShop').addEventListener('click', function() {
    sendMessageToActiveTab({
      action: 'exportShop'
    });
});

// 更多项目按钮
  document.getElementById('moreProjects').addEventListener('click', function() {
    chrome.tabs.create({url: 'https://www.wangzhuanku.com'});
});

// 详细教程按钮
  document.getElementById('tutorial').addEventListener('click', function() {
    chrome.tabs.create({url: 'https://www.wangzhuanku.com/2025/05/26/xycj/'});
  });

  // 加载存储的配置
  loadSettings();
});

// 保存所有设置
function saveSettings() {
  const settings = {
    minWantCount: document.getElementById('minWantCount').value,
    pageCount: document.getElementById('pageCount').value,
    detailCollectSearch: document.getElementById('detailCollectSearch').checked,
    detailIntervalSearch: document.getElementById('detailIntervalSearch').value,
    shopMaxItems: document.getElementById('shopMaxItems').value,
    shopMinWantCount: document.getElementById('shopMinWantCount').value,
    detailCollectShop: document.getElementById('detailCollectShop').checked,
    detailIntervalShop: document.getElementById('detailIntervalShop').value
  };
  
  chrome.storage.local.set({settings: settings}, function() {
    console.log('设置已保存');
  });
}

// 加载存储的设置
function loadSettings() {
  chrome.storage.local.get('settings', function(data) {
    if (data.settings) {
      document.getElementById('minWantCount').value = data.settings.minWantCount || 10;
      document.getElementById('pageCount').value = data.settings.pageCount || 1;
      document.getElementById('detailCollectSearch').checked = data.settings.detailCollectSearch || false;
      document.getElementById('detailIntervalSearch').value = data.settings.detailIntervalSearch || 2;
      document.getElementById('shopMaxItems').value = data.settings.shopMaxItems || 0;
      document.getElementById('shopMinWantCount').value = data.settings.shopMinWantCount || 10;
      document.getElementById('detailCollectShop').checked = data.settings.detailCollectShop || false;
      document.getElementById('detailIntervalShop').value = data.settings.detailIntervalShop || 2;
    }
  });
}

// 向当前激活标签发送消息
function sendMessageToActiveTab(message) {
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    if (tabs[0]) {
      chrome.tabs.sendMessage(tabs[0].id, message, function(response) {
        if (chrome.runtime.lastError) {
          console.error('发送消息错误：', chrome.runtime.lastError);
        } else if (response && response.success) {
          // 保存设置
          saveSettings();
          window.close();
        }
      });
    }
  });
}

// 监听来自content和background的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === 'downloadExcel') {
    // 处理Excel下载请求
    chrome.downloads.download({
      url: request.data.url,
      filename: request.data.filename,
      saveAs: true
    });
    
    sendResponse({success: true});
    return true;
  }
}); 