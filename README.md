# XY爆款数据采集器 V3.0

> 专业的闲鱼商品数据采集浏览器扩展插件

## 🌟 功能特色

### 📊 双模式采集
- **快速模式**：采集基础商品信息（标题、价格、想要人数、状态、链接）
- **详细模式**：采集完整商品数据（包含浏览量、转化比、完整标题、商品描述、配图等）

### 🎯 智能采集
- **搜索商品采集**：支持多页采集，可设置最小想要人数过滤
- **店铺商品采集**：支持指定数量采集，深度挖掘店铺爆款
- **实时进度显示**：美观的悬浮窗实时显示采集进度和状态

### 📈 数据分析
- **转化比计算**：自动计算想要人数/浏览量转化比
- **数据排序**：支持想要人数、浏览量、转化比三列排序
- **状态识别**：自动识别商品在售/已售出状态

### 💾 数据导出
- **HTML可视化**：生成美观的HTML表格，支持排序和详情查看
- **一键导出功能**：HTML内置三种导出方式（图片包、Excel、完整包）
- **智能格式识别**：根据采集模式自动调整导出内容和格式
- **多种导出选择**：纯图片、纯数据、完整数据包任选其一
- **便于分析**：Excel格式便于数据分析，图片包便于素材使用

## 🚀 使用方法

### 安装步骤
1. 下载插件文件到本地
2. 打开Chrome浏览器，进入扩展程序管理页面
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"，选择插件文件夹
5. 安装完成后，浏览器工具栏会出现插件图标

### 搜索商品采集
1. 在闲鱼网站搜索商品，进入搜索结果页面
2. 点击插件图标，展开"搜索商品专区"
3. 设置采集参数：
   - **想要人数过滤**：只采集想要人数≥设定值的商品
   - **采集页数**：设置要采集的页数（最大50页）
   - **详细采集**：勾选后获取完整商品数据
   - **采集间隔**：详细采集时每个商品的间隔时间
4. 点击"开始采集"，等待采集完成
5. 点击"导出数据"下载HTML文件
6. 打开HTML文件，使用内置的导出功能：
   - **📁 导出图片压缩包**：下载所有商品图片
   - **📊 导出Excel表格**：下载商品数据表格
   - **📦 导出完整数据包**：同时下载图片和数据

### 店铺商品采集
1. 进入任意闲鱼店铺页面
2. 点击插件图标，展开"店铺商品专区"
3. 设置采集参数：
   - **采集数量**：选择采集全部或指定数量
   - **想要人数过滤**：只采集想要人数≥设定值的商品
   - **详细采集**：勾选后获取完整商品数据
   - **采集间隔**：详细采集时每个商品的间隔时间
4. 点击"采集店铺商品"，等待采集完成
5. 点击"导出店铺数据"下载HTML文件
6. 打开HTML文件，使用内置的导出功能：
   - **📁 导出图片压缩包**：下载所有商品图片
   - **📊 导出Excel表格**：下载商品数据表格
   - **📦 导出完整数据包**：同时下载图片和数据

## 🎨 界面特色

### 现代化设计
- **渐变背景**：美观的渐变色彩设计
- **卡片布局**：清晰的模块化界面
- **动画效果**：流畅的交互动画
- **响应式**：悬停效果和状态反馈

### 用户体验
- **折叠面板**：节省空间的可折叠区域
- **实时通知**：操作结果的即时反馈
- **进度显示**：详细的采集进度和统计
- **设置记忆**：自动保存用户配置

## 📋 导出数据格式

### HTML表格结构
```
序号 | 商品图片 | 商品标题 | 商品价格 | 想要人数↕ | 浏览量↕ | 转化比↕ | 状态 | 商品链接 | 操作
```

### 一键导出功能
- **📁 图片压缩包**：`XY商品图片_日期.zip`
  - 主图格式：`主图_序号_标题.jpg`
  - 详情图格式：`详情图_序号_图片序号_标题.jpg`
- **📊 Excel表格**：`XY商品数据_日期.xlsx`
  - 基础模式：9列数据（序号、标题、价格、想要人数、浏览量、转化比、状态、商品链接、主图链接）
  - 详细模式：12列数据（增加原价、商品描述、详情图片）
- **📦 完整数据包**：`XY商品完整数据包_日期.zip`
  - 包含商品图片文件夹和Excel数据文件

### 文件命名规则
- 搜索采集：`XY商品数据（搜索商品采集-详细模式）`
- 店铺采集：`XY商品数据（店铺商品采集-快速模式）`

### 数据字段说明
- **基础数据**：标题、价格、想要人数、状态、链接、图片
- **详细数据**：完整标题、浏览量、转化比、商品描述、完整配图
- **计算字段**：转化比 = 想要人数 / 浏览量 × 100%

## ⚙️ 技术特点

### 核心技术
- **Manifest V3**：使用最新的Chrome扩展API
- **异步采集**：Promise-based异步数据采集
- **DOM解析**：多重选择器确保数据准确性
- **一键导出**：集成JSZip和XLSX库，支持图片和数据导出
- **错误处理**：完善的异常处理和用户反馈

### 性能优化
- **智能等待**：页面加载状态检测
- **内存管理**：及时清理临时数据
- **批量处理**：高效的数据批量操作
- **缓存机制**：本地存储用户配置

### 安全特性
- **权限最小化**：只请求必要的浏览器权限
- **数据隔离**：采集数据本地存储，不上传
- **错误恢复**：采集中断后可继续操作

## 🔧 配置选项

### 采集参数
- **想要人数过滤**：0-999999（默认10）
- **采集页数**：1-50页（默认1页）
- **详细采集间隔**：1-10秒（默认2秒）
- **采集数量**：全部/10/20/50/100条

### 高级设置
- **自动保存配置**：记住用户设置
- **实时进度显示**：采集过程可视化
- **错误重试机制**：网络异常自动重试
- **数据完整性检查**：确保导出数据准确

## 📞 技术支持

- **官方网站**：[网赚库 - wangzhuanku.com](https://www.wangzhuanku.com)
- **详细教程**：[使用教程](https://www.wangzhuanku.com/2025/05/26/xycj/)
- **版本信息**：V3.0
- **更新日期**：2025年5月

## 📄 版权声明

本插件由网赚库开发，仅供学习和研究使用。请遵守相关网站的使用条款，合理使用数据采集功能。

## 🔧 最新更新 (V3.0)

### 🎯 核心新功能：一键导出图片和文案
- **📁 图片压缩包导出**：一键下载所有商品的主图和详情图，自动打包为ZIP格式
- **📊 Excel表格导出**：将所有商品文案数据导出为Excel文件，支持完整的商品信息
- **📦 完整数据包导出**：同时包含图片文件夹和Excel数据文件的组合包
- **🎨 美观导出界面**：在生成的HTML中新增专业的导出工具栏，三个彩色按钮一目了然
- **📈 实时进度显示**：导出过程中显示详细的进度条和状态信息
- **🔄 智能错误处理**：图片下载失败时自动跳过并统计成功率

### 📊 导出功能特点
- **智能文件命名**：图片按"主图_序号_标题.jpg"和"详情图_序号_图片序号_标题.jpg"格式命名
- **Excel列宽优化**：自动设置合适的列宽，便于查看和编辑
- **模式差异化支持**：详细采集模式包含原价、商品描述、详情图片等额外列
- **跨域图片处理**：支持跨域图片下载，确保图片文件完整性
- **CDN库支持**：使用JSZip和XLSX等专业库，确保导出功能稳定可靠

### 翻页功能重大改进
- **修复翻页问题**：解决了设置采集20页但只采集4页的问题
- **增强翻页机制**：新增4种翻页方式，确保翻页成功率
- **智能重试系统**：翻页失败时自动重试3次，避免因网络波动导致的采集中断
- **页面变化检测**：多维度检测页面变化，提高翻页成功识别率

### 用户体验优化
- **统一完成提示**：修复了店铺采集和搜索采集完成提示不一致的问题
- **双重反馈机制**：现在所有采集完成后都有弹窗提示 + 悬浮窗绿色提示
- **错误处理统一**：采集失败时也提供一致的错误反馈

### 排序功能修复
- **修复排序问题**：解决了想要人数和浏览量排序不生效的问题
- **正则表达式修复**：修复了关键的正则表达式错误 `/[^d.-]/g` → `/[^\d.-]/g`
- **智能排序逻辑**：改进了"无数据"项的排序处理，无数据项排在最后并按想要人数二级排序
- **表头保护**：确保排序时表头行不会消失，保持表格结构完整
- **排序指示器**：修复了排序方向指示器的显示问题

### 翻页机制详解
1. **方法1**：点击下一页按钮（支持多种按钮样式）
2. **方法2**：点击具体页码数字
3. **方法3**：使用页码输入框跳转
4. **方法4**：修改URL参数跳转

### 错误处理优化
- **网络异常处理**：翻页失败时等待3秒后重试
- **页面状态检查**：实时检测页码变化和内容加载
- **智能判断**：区分真正的翻页失败和网络延迟

---

**感谢使用XY爆款数据采集器！** 