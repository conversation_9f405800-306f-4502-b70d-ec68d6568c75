<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>XY爆款数据采集器</title>
  <style>
    body {
      width: 340px;
      padding: 0;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
    }
    
    .header {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      color: white;
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    .header h2 {
      margin: 0 0 5px 0;
      font-size: 20px;
      font-weight: 600;
      text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    .header .subtitle {
      font-size: 12px;
      opacity: 0.9;
      margin: 0;
    }
    
    .container {
      padding: 20px;
      background: #f8f9fa;
    }
    
    .section {
      border: 1px solid #e8e8e8;
      margin-bottom: 16px;
      border-radius: 12px;
      background: white;
      box-shadow: 0 2px 8px rgba(0,0,0,0.06);
      overflow: hidden;
      transition: all 0.3s ease;
    }
    
    .section:hover {
      box-shadow: 0 4px 16px rgba(0,0,0,0.1);
      transform: translateY(-1px);
    }
    
    .section-header {
      background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
      padding: 14px 18px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #333;
      transition: all 0.3s ease;
      border-bottom: 1px solid #e8e8e8;
    }
    
    .section-header:hover {
      background: linear-gradient(135deg, #e8e8e8 0%, #d9d9d9 100%);
    }
    
    .section-header.active {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      color: white;
      border-bottom-color: #1890ff;
    }
    
    .section-content {
      padding: 0;
      max-height: 0;
      overflow: hidden;
      transition: all 0.3s ease;
    }
    
    .section-content.active {
      padding: 18px;
      max-height: 500px;
    }
    
    .arrow {
      transition: transform 0.3s ease;
      color: #666;
      font-size: 12px;
    }
    
    .section-header.active .arrow {
      color: white;
    }
    
    .arrow.active {
      transform: rotate(180deg);
    }
    
    .input-group {
      margin: 12px 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .input-group label {
      white-space: nowrap;
      font-size: 13px;
      color: #555;
      min-width: fit-content;
      font-weight: 500;
    }
    
    .input-group select,
    .input-group input {
      flex: 1;
      padding: 10px 12px;
      border: 2px solid #e8e8e8;
      border-radius: 8px;
      font-size: 13px;
      transition: all 0.3s ease;
      background: white;
    }
    
    .input-group select:focus,
    .input-group input:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 3px rgba(24,144,255,0.1);
      outline: none;
      transform: translateY(-1px);
    }
    
    .checkbox-group {
      display: flex;
      align-items: center;
      margin: 12px 0;
      padding: 10px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e8e8e8;
    }
    
    .checkbox-group input[type="checkbox"] {
      margin-right: 10px;
      transform: scale(1.2);
      accent-color: #1890ff;
    }
    
    .checkbox-group label {
      font-size: 13px;
      color: #555;
      font-weight: 500;
    }
    
    button {
      width: 100%;
      margin: 8px 0;
      padding: 12px 16px;
      border: none;
      border-radius: 8px;
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      color: white;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(24,144,255,0.3);
    }
    
    button:hover {
      background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(24,144,255,0.4);
    }
    
    button:active {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(24,144,255,0.3);
    }
    
    #stopCollect {
      background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
      box-shadow: 0 2px 8px rgba(255,77,79,0.3);
    }
    
    #stopCollect:hover {
      background: linear-gradient(135deg, #ff7875 0%, #ffa39e 100%);
      box-shadow: 0 4px 16px rgba(255,77,79,0.4);
    }
    
    .footer {
      display: flex;
      gap: 12px;
      margin-top: 20px;
    }
    
    .footer button {
      flex: 1;
      background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
      box-shadow: 0 2px 8px rgba(82,196,26,0.3);
    }
    
    .footer button:hover {
      background: linear-gradient(135deg, #73d13d 0%, #95de64 100%);
      box-shadow: 0 4px 16px rgba(82,196,26,0.4);
    }
    
    .status-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #52c41a;
      margin-right: 8px;
      animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
    
    .feature-badge {
      display: inline-block;
      background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
      font-weight: 600;
      margin-left: 8px;
      text-transform: uppercase;
    }
  </style>
</head>
<body>
  <div class="header">
    <h2><span class="status-indicator"></span>XY爆款数据采集器 V3.0</h2>
    <div class="subtitle">网赚库：wangzhuanku.com</div>
  </div>
  
  <div class="container">
    <!-- 搜索商品专区 -->
    <div class="section">
      <div class="section-header" id="searchHeader">
        <span>🔍 搜索商品专区<span class="feature-badge">Hot</span></span>
        <span class="arrow">▼</span>
      </div>
      <div class="section-content" id="searchContent">
        <div class="input-group">
          <label>仅采集"想要"≥：</label>
          <input type="number" id="minWantCount" min="0" value="10" placeholder="最小想要人数">
        </div>
        <div class="input-group">
          <label>采集页数(最大50)：</label>
          <input type="number" id="pageCount" min="1" max="50" value="1" placeholder="页数">
        </div>
        <div class="checkbox-group">
          <input type="checkbox" id="detailCollectSearch">
          <label for="detailCollectSearch">🚀 启用详细采集（获取完整数据）</label>
        </div>
        <div class="input-group">
          <label>详细采集间隔(秒)：</label>
          <input type="number" id="detailIntervalSearch" min="1" max="10" value="2" placeholder="间隔秒数">
        </div>
        <button id="collectSearch">🎯 开始采集</button>
        <button id="stopCollect">⏹️ 停止采集</button>
        <button id="exportSearch">📊 导出数据</button>
      </div>
    </div>

    <!-- 店铺商品专区 -->
    <div class="section">
      <div class="section-header" id="shopHeader">
        <span>🏪 店铺商品专区<span class="feature-badge">New</span></span>
        <span class="arrow">▼</span>
      </div>
      <div class="section-content" id="shopContent">
        <div class="input-group">
          <label>采集数量：</label>
          <select id="shopMaxItems">
            <option value="0">采集全部</option>
            <option value="10">前10条</option>
            <option value="20">前20条</option>
            <option value="50">前50条</option>
            <option value="100">前100条</option>
          </select>
        </div>
        <div class="input-group">
          <label>仅采集"想要"≥：</label>
          <input type="number" id="shopMinWantCount" min="0" value="10" placeholder="最小想要人数">
        </div>
        <div class="checkbox-group">
          <input type="checkbox" id="detailCollectShop">
          <label for="detailCollectShop">🚀 启用详细采集（获取完整数据）</label>
        </div>
        <div class="input-group">
          <label>详细采集间隔(秒)：</label>
          <input type="number" id="detailIntervalShop" min="1" max="10" value="2" placeholder="间隔秒数">
        </div>
        <button id="collectShop">🎯 采集店铺商品</button>
        <button id="exportShop">📊 导出店铺数据</button>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="footer">
      <button id="moreProjects">🌟 更多项目</button>
      <button id="tutorial">📖 详细教程</button>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html> 