#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试滑动功能的脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from core.config_manager import ConfigManager
import logging

def test_scroll_functionality():
    """测试滑动功能"""
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    logger.info("开始测试滑动功能...")
    
    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 初始化采集引擎
        engine = CollectorEngine(config_manager)
        
        # 测试采集功能（包含滑动）
        logger.info("测试采集虚拟商品（包含滑动到底部功能）...")

        def progress_callback(progress_info):
            current_page = progress_info.get('current_page', 0)
            total_pages = progress_info.get('total_pages', 1)
            total_items = progress_info.get('total_items', 0)
            logger.info(f"进度更新: 第{current_page}/{total_pages}页, 已采集{total_items}个商品")
        
        # 采集数据
        products = engine.collect_search_data(
            keyword="虚拟商品",
            max_pages=2,  # 测试2页
            min_want_count=5,  # 降低门槛便于测试
            progress_callback=progress_callback
        )
        
        if products:
            logger.info(f"✅ 测试成功！采集到 {len(products)} 个商品")
            
            # 显示前3个商品的信息
            for i, product in enumerate(products[:3]):
                logger.info(f"商品 {i+1}:")
                logger.info(f"  标题: {product.get('title', 'N/A')}")
                logger.info(f"  价格: {product.get('price', 'N/A')}")
                logger.info(f"  想要人数: {product.get('wantCount', 'N/A')}")
                logger.info(f"  链接: {product.get('link', 'N/A')}")
                logger.info("  ---")
                
        else:
            logger.error("❌ 测试失败！未采集到任何商品")
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        try:
            if 'engine' in locals() and engine.page:
                engine.cleanup()
                logger.info("清理完成")
        except:
            pass

def test_scroll_methods_only():
    """仅测试滑动方法"""
    
    logging.basicConfig(level=logging.DEBUG)
    logger = logging.getLogger(__name__)
    
    try:
        config_manager = ConfigManager()
        engine = CollectorEngine(config_manager)
        
        # 初始化浏览器
        if not engine.init_browser(headless=False):  # 使用有头模式便于观察
            logger.error("浏览器初始化失败")
            return
        
        # 打开测试页面
        engine.page.get("https://www.goofish.com/search?q=虚拟商品")
        
        import time
        time.sleep(3)
        
        logger.info("开始测试滑动功能...")
        
        # 测试滑动到底部
        engine._scroll_to_bottom_and_load_all()
        
        logger.info("滑动测试完成，请观察浏览器页面")
        
        # 保持页面打开5秒便于观察
        time.sleep(5)
        
    except Exception as e:
        logger.error(f"滑动测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            if 'engine' in locals() and engine.page:
                engine.cleanup()
        except:
            pass

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 完整功能测试（包含滑动）")
    print("2. 仅测试滑动功能")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        test_scroll_functionality()
    elif choice == "2":
        test_scroll_methods_only()
    else:
        print("无效选择，执行完整功能测试...")
        test_scroll_functionality()
