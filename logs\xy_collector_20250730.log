2025-07-30 14:34:29 - XYCollector - INFO - XY采集器启动中...
2025-07-30 14:34:29 - XYCollector - INFO - 需要进行授权验证
2025-07-30 14:34:30 - XYCollector - INFO - XY采集器启动完成
2025-07-30 14:43:08 - XYCollector - INFO - Logger test successful
2025-07-30 14:43:57 - XYCollector - INFO - 日志模块测试成功
2025-07-30 14:44:58 - XYCollector - INFO - 日志系统测试成功
2025-07-30 14:48:44 - XYCollector - INFO - XY采集器启动中...
2025-07-30 14:48:44 - XYCollector - INFO - XY采集器启动完成
2025-07-30 15:45:06 - XYCollector - INFO - XY采集器启动中...
2025-07-30 15:45:07 - XYCollector - INFO - XY采集器启动完成
2025-07-30 15:47:32 - XYCollector - INFO - XY采集器启动中...
2025-07-30 15:47:32 - XYCollector - INFO - XY采集器启动完成
2025-07-30 15:48:58 - XYCollector - INFO - XY采集器启动中...
2025-07-30 15:48:58 - XYCollector - INFO - XY采集器启动完成
2025-07-30 15:56:29 - XYCollector - INFO - XY采集器启动中...
2025-07-30 15:56:30 - XYCollector - INFO - XY采集器启动完成
2025-07-30 15:57:35 - XYCollector - INFO - XY采集器启动中...
2025-07-30 15:57:36 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:05:07 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:05:07 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:06:05 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:06:05 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:12:58 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:12:59 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:18:25 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:18:26 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:19:05 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:19:05 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:25:42 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:25:42 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:27:39 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:27:40 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:28:18 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:28:19 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:31:31 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:31:31 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:32:51 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:32:51 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:33:29 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:33:30 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:38:14 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:38:15 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:38:49 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:38:49 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:44:43 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:44:43 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:49:36 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:49:36 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:52:12 - XYCollector - INFO - 🧪 开始测试搜索API修复效果
2025-07-30 16:52:12 - XYCollector - INFO - ✅ 检测到有效的登录cookies
2025-07-30 16:52:12 - XYCollector - INFO - 📊 Cookies总数: 12
2025-07-30 16:52:12 - XYCollector - INFO - ✅ 核心Cookie _m_h5_tk: 存在
2025-07-30 16:52:12 - XYCollector - INFO - ✅ 核心Cookie _tb_token_: 存在
2025-07-30 16:52:12 - XYCollector - INFO - ✅ 核心Cookie t: 存在
2025-07-30 16:52:12 - XYCollector - INFO - 🔍 测试关键词: 手机
2025-07-30 16:58:03 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:58:03 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:58:54 - XYCollector - WARNING - ❌ 测试失败：未获取到任何数据
2025-07-30 16:58:54 - XYCollector - INFO - 💡 可能的原因：
2025-07-30 16:58:54 - XYCollector - INFO -    1. 服务器过载，请稍后重试
2025-07-30 16:58:54 - XYCollector - INFO -    2. 登录状态已过期，请重新登录
2025-07-30 16:58:54 - XYCollector - INFO -    3. API参数需要调整
2025-07-30 16:58:54 - XYCollector - INFO - 🔚 测试完成
2025-07-30 17:47:14 - XYCollector - INFO - 🔍 开始测试使用真实浏览器参数的搜索功能
2025-07-30 17:47:14 - XYCollector - INFO - 📱 正在打开登录浏览器...
2025-07-30 17:47:14 - XYCollector - ERROR - ❌ 测试过程中出现错误: 'CollectorEngine' object has no attribute 'open_login_browser'
2025-07-30 17:47:14 - XYCollector - ERROR - 详细错误信息: Traceback (most recent call last):
  File "E:\Cursor源码\XY商品采集插件\test_real_params.py", line 34, in test_real_params
    if collector.open_login_browser():
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'CollectorEngine' object has no attribute 'open_login_browser'

2025-07-30 17:47:58 - XYCollector - INFO - 🔍 开始简单测试
2025-07-30 17:47:58 - XYCollector - INFO - ✅ 初始化成功
2025-07-30 17:47:58 - XYCollector - INFO - 📋 构建的搜索参数: {'jsv': '2.7.2', 'appKey': '********', 't': '*************', 'sign': '2f0c24cf832e1606c278b291803742be', 'v': '1.0', 'type': 'originaljson', 'accountSite': 'xianyu', 'dataType': 'json', 'timeout': '20000', 'api': 'mtop.taobao.idlehome.home.webpc.feed', 'sessionOption': 'AutoLoginOnly', 'spm_cnt': 'a21ybx.home.0.0', 'data': '{"keyword":"手机","page":1,"pageSize":20,"sortType":"default"}'}
2025-07-30 17:47:58 - XYCollector - INFO - ✅ 测试完成
2025-07-30 17:48:35 - XYCollector - INFO - 🔍 开始测试API请求
2025-07-30 17:48:35 - XYCollector - INFO - 📋 请求参数: {'jsv': '2.7.2', 'appKey': '********', 't': '*************', 'sign': 'e37c8f1105b94fe94918ace408b1fda4', 'v': '1.0', 'type': 'originaljson', 'accountSite': 'xianyu', 'dataType': 'json', 'timeout': '20000', 'api': 'mtop.taobao.idlehome.home.webpc.feed', 'sessionOption': 'AutoLoginOnly', 'spm_cnt': 'a21ybx.home.0.0', 'data': '{"keyword":"手机","page":1,"pageSize":20,"sortType":"default"}'}
2025-07-30 17:48:35 - XYCollector - INFO - 🌐 请求URL: https://h5api.m.goofish.com/h5/mtop.taobao.idlehome.home.webpc.feed/1.0/
2025-07-30 17:48:35 - XYCollector - INFO - 📡 发送API请求...
2025-07-30 17:48:35 - XYCollector - INFO - 📊 响应状态码: 200
2025-07-30 17:48:35 - XYCollector - INFO - 📊 响应头: {'Date': 'Wed, 30 Jul 2025 09:48:37 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Content-Length': '111', 'Connection': 'keep-alive', 'Vary': 'Accept-Encoding', 'Set-Cookie': 'cookie2=1718220ae3f4d4d745665a4ba691f105;Path=/;Domain=.goofish.com;Max-Age=-1;HttpOnly, mtop_partitioned_detect=1;Path=/;Domain=.goofish.com;Max-Age=5400;SameSite=None;Secure;Partitioned, _m_h5_tk=af0ae1a8944ce3e36b9dbd67531d6a04_1753878277186;Path=/;Domain=.goofish.com;Max-Age=5400;SameSite=None;Secure, _m_h5_tk_enc=d2a41d3c11e9865beaf79138c7f5de75;Path=/;Domain=.goofish.com;Max-Age=5400;SameSite=None;Secure', 's_tid': '2150451d17538689171818524e1aff', 'x-node': 'dafe669a6659e576db3ae0189a6a53f3', 'Access-Control-Allow-Origin': 'https://www.goofish.com', 's_ip': '457079564a4a776e793147593262773d', 'x-eagleeye-id': '2150451d17538689171818524e1aff', 's_v': '*******', 'Pragma': 'no-cache', 'P3P': "CP='CURa ADMa DEVa PSAo PSDo OUR BUS UNI PUR INT DEM STA PRE COM NAV OTC NOI DSP COR'", 's_tag': '285873024335892|134217728^|^^', 'Access-Control-Expose-Headers': 'x-eagleeye-id', 'Cache-Control': 'no-cache', 'Access-Control-Allow-Credentials': 'true', 's_group': 'tao-session', 's_status': 'STATUS_NOT_EXISTED', 's_ucode': 'CN:CENTER', 'X-Powered-By': 'm.taobao.com', 'Ups-Target-Key': 'mtop.uncenter.idle-home', 'X-protocol': 'HTTP/1.1', 'EagleEye-TraceId': '2150451d17538689171818524e1aff', 'Strict-Transport-Security': 'max-age=0', 's-brt': '4', 's-rt': '8', 's-cunit': '0'}
2025-07-30 17:48:35 - XYCollector - INFO - ✅ 成功获取响应数据
2025-07-30 17:48:35 - XYCollector - INFO - 📋 响应结构: ['api', 'data', 'ret', 'v']
2025-07-30 17:48:35 - XYCollector - INFO - 🔍 返回码: ['FAIL_SYS_TOKEN_EMPTY::令牌为空']
2025-07-30 17:48:35 - XYCollector - INFO - 🔍 数据字段: <class 'dict'>
2025-07-30 17:48:35 - XYCollector - INFO - 🔍 数据子字段: []
2025-07-30 17:48:35 - XYCollector - INFO - 📄 完整响应内容: {
  "api": "mtop.taobao.idlehome.home.webpc.feed",
  "data": {},
  "ret": [
    "FAIL_SYS_TOKEN_EMPTY::令牌为空"
  ],
  "v": "1.0"
}
2025-07-30 17:54:31 - XYCollector - INFO - 🔍 开始测试认证数据保存和加载功能
2025-07-30 17:54:31 - XYCollector - INFO - 📝 未找到认证数据文件
2025-07-30 17:54:31 - XYCollector - INFO - 💡 请先运行程序并登录以生成认证数据
2025-07-30 17:54:31 - XYCollector - INFO - 
📖 生成认证数据的步骤:
2025-07-30 17:54:31 - XYCollector - INFO - 1. 运行主程序: python main.py
2025-07-30 17:54:31 - XYCollector - INFO - 2. 点击登录按钮
2025-07-30 17:54:31 - XYCollector - INFO - 3. 在浏览器中完成登录
2025-07-30 17:54:31 - XYCollector - INFO - 4. 登录成功后会自动保存完整认证数据
2025-07-30 17:55:21 - XYCollector - INFO - XY采集器启动中...
2025-07-30 17:55:22 - XYCollector - INFO - XY采集器启动完成
2025-07-30 18:08:11 - main - INFO - ==================================================
2025-07-30 18:08:11 - main - INFO - 开始测试更新后的API
2025-07-30 18:08:11 - main - INFO - ==================================================
2025-07-30 18:08:11 - main - INFO - 
1. 测试直接API请求...
2025-07-30 18:08:11 - test_api - INFO - 发送API请求到: https://h5api.m.goofish.com/h5/mtop.taobao.idlemtopsearch.pc.search/1.0/
2025-07-30 18:08:11 - test_api - INFO - 请求参数: {'jsv': '2.7.2', 'appKey': '********', 't': '*************', 'sign': '039b6ca530cf01b5f962a20c679f68ab', 'v': '1.0', 'type': 'originaljson', 'accountSite': 'xianyu', 'dataType': 'json', 'timeout': '20000', 'api': 'mtop.taobao.idlemtopsearch.pc.search', 'sessionOption': 'AutoLoginOnly', 'spm_cnt': 'a21ybx.search.0.0', 'data': '{"keyword":"手机","pageNum":1,"pageSize":20,"sortType":"default","categoryId":"","priceRange":"","location":""}'}
2025-07-30 18:08:11 - test_api - INFO - 响应状态码: 200
2025-07-30 18:08:11 - test_api - INFO - 响应头: {'Date': 'Wed, 30 Jul 2025 10:08:12 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'Vary': 'Accept-Encoding', 'Server': 'Tengine', 'Cache-Control': 'no-store', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Allow-Origin': 'https://www.goofish.com', 'Set-Cookie': 'x5secdata=xgcc45c2f026d7cfadja721447cbbf2f81bc5957e6ae4e490fc01753870092a-717315356a1781995901abaad3eaa0xianyuSpace_default__bx__h5api.m.goofish.com:443/h5/mtop.taobao.idlemtopsearch.pc.search/1.0; Max-Age=20; Expires=Wed, 30-Jul-2025 10:08:32 GMT; Domain=goofish.com; Path=/;SameSite=None;Secure', 'bxpunish': '1', 'EagleEye-TraceId': '213e04dd17538700927528839e1368', 'Content-Encoding': 'gzip'}
2025-07-30 18:08:11 - test_api - INFO - API响应成功: {
  "api": "mtop.taobao.idlemtopsearch.pc.search",
  "v": "1.0",
  "ret": [
    "RGV587_ERROR::SM::哎哟喂,被挤爆啦,请稍后重试!"
  ],
  "data": {
    "url": "https://passport.goofish.com/mini_login.htm?lang=zh_cn&appName=xianyu&appEntrance=baxia&redirectType=iframeRedirect&styleType=vertical&bizParams=&notLoadSsoView=true&notKeepLogin=false&isMobile=false&qrCodeFirst=false&rnd=0.7405678221745524&returnUrl=https%3a%2f%2fh5api.m.goofish.com:443/h5/mtop.taobao.idlemtopsearch.pc.search/1.0%2F_____tmd_____%2Fpage%2Fmtoph5_close_iframe_page%3Frand%3DS3WxGHAgAt756EpznwfNzJq2AFA2qBNla3j6EINUS8We9dazM_iKElp8DwVSHZUevpC41Bx7RzivXIj9RnZgdg%26uuid%3D721447cbbf2f81bc5957e6ae4e490fc0%26_lgt_%3D721447cbbf2f81bc5957e6ae4e490fc0___305220___7d98946434a22920c7857598b434055f___eaebc79cac1eb5d2f7d8b4595e00ec73344a42d5a0b8cf56539c823cd24ac06c092f67171550cb6cbbc4aaaa937e5ef4f60277010dffec5689671a57f915809888d25ef3220a7c2464a290cff32056669f3d6a459ad2c296ee8cbb5a8d67c1a029af9254ddea98f951f5ea79ca6d68afacb857c81ed97566c7ec74b7eb613fa519b4c3107e712c0b12b48606e859d16ccbd4fb8cf17cff382cca796b27b42f93bdd660982d155ad900bd3837f5da3e57ca78869e9450dde1ca0616ecc84d1bef86a07297d351a5f1954e205f0907ed8a8573efd9da76ac422830f9dd5d51430d445241acd34e73e352df82d7bd88f7603324f4eeb0c568ee25b866619f48bfbe5980694235e74c5175910d39cf220d673f931d9577c39d84ccd5dac9edfe35c1606c4441ac1518fa4d08bfaae19c43275893cad2ac6c66305669cbb46e10dce12e3d1a4f25f20a516691d08199f51c3a5f1a91dade58bb45606e7876df4b8e206704908d5931eab3a14d2d187e23027107d3735fdbe579bd9929e929563feb55",
    "h5url": "https://passport.goofish.com/mini_login.htm?appName=xianyu&appEntrance=baxia&isMobile=true&redirectType=iframeRedirect&returnUrl=https%3a%2f%2fh5api.m.goofish.com:443/h5/mtop.taobao.idlemtopsearch.pc.search/1.0%2F_____tmd_____%2Fpage%2Fmtoph5_close_iframe_page%3Frand%3DS3WxGHAgAt756EpznwfNzJq2AFA2qBNla3j6EINUS8We9dazM_iKElp8DwVSHZUevpC41Bx7RzivXIj9RnZgdg%26uuid%3D721447cbbf2f81bc5957e6ae4e490fc0%26_lgt_%3D721447cbbf2f81bc5957e6ae4e490fc0___305220___7d98946434a22920c7857598b434055f___eaebc79cac1eb5d2f7d8b4595e00ec73344a42d5a0b8cf56539c823cd24ac06c092f67171550cb6cbbc4aaaa937e5ef4f60277010dffec5689671a57f915809888d25ef3220a7c2464a290cff32056669f3d6a459ad2c296ee8cbb5a8d67c1a029af9254ddea98f951f5ea79ca6d68afacb857c81ed97566c7ec74b7eb613fa519b4c3107e712c0b12b48606e859d16ccbd4fb8cf17cff382cca796b27b42f93bdd660982d155ad900bd3837f5da3e57ca78869e9450dde1ca0616ecc84d1bef86a07297d351a5f1954e205f0907ed8a8573efd9da76ac422830f9dd5d51430d445241acd34e73e352df82d7bd88f7603324f4eeb0c568ee25b866619f48bfbe5980694235e74c5175910d39cf220d673f931d9577c39d84ccd5dac9edfe35c1606c4441ac1518fa4d08bfaae19c43275893cad2ac6c66305669cbb46e10dce12e3d1a4f25f20a516691d08199f51c3a5f1a91dade58bb45606e7876df4b8e206704908d5931eab3a14d2d187e23027107d3735fdbe579bd9929e929563feb55",
    "dialogSize": {
      "width": "856px",
      "height": "454px"
    }
  },
  "dialogSize": {
    "width": "856px",
    "height": "454px"
  }
}
2025-07-30 18:08:11 - main - INFO - ✅ 直接API请求测试成功
2025-07-30 18:08:11 - main - INFO - 
2. 测试采集引擎...
2025-07-30 18:08:11 - test_collector - ERROR - 测试失败: CollectorEngine.__init__() missing 1 required positional argument: 'config_manager'
2025-07-30 18:08:11 - main - ERROR - ❌ 采集引擎测试失败
2025-07-30 18:08:11 - main - INFO - 
==================================================
2025-07-30 18:08:11 - main - INFO - 测试完成
2025-07-30 18:08:11 - main - INFO - ==================================================
2025-07-30 18:10:48 - XYCollector - INFO - XY采集器启动中...
2025-07-30 18:10:49 - XYCollector - INFO - XY采集器启动完成
2025-07-30 18:16:34 - XYCollector - INFO - XY采集器启动中...
2025-07-30 18:16:34 - XYCollector - INFO - XY采集器启动完成
2025-07-30 18:20:56 - main - INFO - 开始测试基于DrissionPage网络监听的真实API
2025-07-30 18:20:56 - test_real_api - INFO - ============================================================
2025-07-30 18:20:56 - test_real_api - INFO - 测试基于网络监听的真实API请求
2025-07-30 18:20:56 - test_real_api - INFO - ============================================================
2025-07-30 18:20:56 - test_real_api - INFO - 请求URL: https://h5api.m.goofish.com/h5/mtop.taobao.idlemtopsearch.pc.search/1.0/
2025-07-30 18:20:56 - test_real_api - INFO - Token: 77b3d1313ea39ec76893592920aa1717_1753878327365
2025-07-30 18:20:56 - test_real_api - INFO - Token部分: 77b3d1313ea39ec76893592920aa1717
2025-07-30 18:20:56 - test_real_api - INFO - 签名字符串: 77b3d1313ea39ec76893592920aa1717&1753870856801&********&{"keyword":"iPhone","pageNum":1,"pageSize":20,"sortType":"default"}
2025-07-30 18:20:56 - test_real_api - INFO - 生成签名: 345a8bf641d950714e0a70453504632a
2025-07-30 18:20:56 - test_real_api - INFO - 搜索关键词: iPhone
2025-07-30 18:20:56 - test_real_api - INFO - 响应状态码: 200
2025-07-30 18:20:56 - test_real_api - INFO - 响应头: {'Date': 'Wed, 30 Jul 2025 10:20:58 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'Vary': 'Accept-Encoding', 'Server': 'Tengine', 'Cache-Control': 'no-store', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Allow-Origin': 'https://www.goofish.com', 'Set-Cookie': 'x5secdata=xg7eda4604d34f5797ja61e2773fb3c49ce3c4bca0b60f46d3391753870858a-717315356a-264295618abaad3ek3325557607a23aOVS4Q226eAihv4t6KCpXiwxianyuSpace_default__bx__h5api.m.goofish.com:443/h5/mtop.taobao.idlemtopsearch.pc.search/1.0; Max-Age=20; Expires=Wed, 30-Jul-2025 10:21:18 GMT; Domain=goofish.com; Path=/;SameSite=None;Secure', 'bxpunish': '1', 'EagleEye-TraceId': '2150461917538708584275248e1379', 'Content-Encoding': 'gzip'}
2025-07-30 18:20:56 - test_real_api - INFO - ============================================================
2025-07-30 18:20:56 - test_real_api - INFO - API响应成功!
2025-07-30 18:20:56 - test_real_api - INFO - ============================================================
2025-07-30 18:20:56 - test_real_api - INFO - 返回状态: ['RGV587_ERROR::SM::哎哟喂,被挤爆啦,请稍后重试!']
2025-07-30 18:20:56 - test_real_api - ERROR - ❌ API调用失败: RGV587_ERROR::SM::哎哟喂,被挤爆啦,请稍后重试!
2025-07-30 18:20:56 - test_real_api - INFO - 完整响应（前1000字符）: {
  "api": "mtop.taobao.idlemtopsearch.pc.search",
  "v": "1.0",
  "ret": [
    "RGV587_ERROR::SM::哎哟喂,被挤爆啦,请稍后重试!"
  ],
  "data": {
    "url": "https://passport.goofish.com/mini_login.htm?lang=zh_cn&appName=xianyu&appEntrance=baxia&redirectType=iframeRedirect&styleType=vertical&bizParams=&notLoadSsoView=true&notKeepLogin=false&isMobile=false&qrCodeFirst=false&rnd=0.7405678221745524&returnUrl=https%3a%2f%2fh5api.m.goofish.com:443/h5/mtop.taobao.idlemtopsearch.pc.search/1.0%2F_____tmd_____%2Fpage%2Fmtoph5_close_iframe_page%3Frand%3DS3WxGHAgAt756EpznwfNzJq2AFA2qBNla3j6EINUS8We9dazM_iKElp8DwVSHZUevpC41Bx7RzivXIj9RnZgdg%26uuid%3D61e2773fb3c49ce3c4bca0b60f46d339%26_lgt_%3D61e2773fb3c49ce3c4bca0b60f46d339___292624___4a90581edee836ed64f555cb54eebc89___eaebc79cac1eb5d2f7d8b4595e00ec73344a42d5a0b8cf56539c823cd24ac06c092f67171550cb6cbbc4aaaa937e5ef4f60277010dffec5689671a57f915809888d25ef3220a7c2464a290cff320566685ef80c7a9e147099375e4cba0ee26bb0330ac44d96bbf34f75f0a1feb169d32bf0cb464fb7a1f4916d81...
2025-07-30 18:20:56 - main - INFO - 
🎉 测试成功！API请求正常工作。
2025-07-30 18:26:32 - XYCollector - INFO - XY采集器启动中...
2025-07-30 18:26:32 - XYCollector - INFO - XY采集器启动完成
2025-07-30 18:36:08 - XYCollector - INFO - XY采集器启动中...
2025-07-30 18:36:08 - XYCollector - INFO - XY采集器启动完成
2025-07-30 18:54:08 - XYCollector - INFO - XY采集器启动中...
2025-07-30 18:54:09 - XYCollector - INFO - XY采集器启动完成
2025-07-30 18:59:55 - XYCollector - INFO - XY采集器启动中...
2025-07-30 18:59:56 - XYCollector - INFO - XY采集器启动完成
2025-07-30 19:08:59 - XYCollector - INFO - XY采集器启动中...
2025-07-30 19:09:00 - XYCollector - INFO - XY采集器启动完成
2025-07-30 19:19:32 - XYCollector - INFO - 用户退出程序
2025-07-30 19:26:21 - XYCollector - INFO - XY采集器启动中...
2025-07-30 19:26:22 - XYCollector - INFO - XY采集器启动完成
