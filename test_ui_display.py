#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI数据展示功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication
from ui.main_window import MainWindow
from core.config_manager import ConfigManager
import logging

def test_ui_display():
    """测试UI数据展示功能"""
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    logger.info("开始测试UI数据展示功能...")
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 创建主窗口
        window = MainWindow(config_manager)
        window.show()
        
        logger.info("✅ UI窗口已启动")
        logger.info("📝 请在界面中:")
        logger.info("   1. 输入搜索关键词: 虚拟商品")
        logger.info("   2. 设置想要人数: 1")
        logger.info("   3. 设置页数: 2")
        logger.info("   4. 点击搜索按钮")
        logger.info("   5. 观察数据是否正确显示在表格中")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ui_display()
