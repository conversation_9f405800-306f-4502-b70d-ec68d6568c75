#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from core.config_manager import ConfigManager
import logging

def test_fixed_functionality():
    """测试修复后的功能"""
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    logger.info("开始测试修复后的功能...")
    
    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 初始化采集引擎
        engine = CollectorEngine(config_manager)
        
        # 测试采集功能
        logger.info("测试采集虚拟商品（修复版本）...")
        
        def progress_callback(progress_info):
            """正确的进度回调函数"""
            current_page = progress_info.get('current_page', 0)
            total_pages = progress_info.get('total_pages', 1)
            total_items = progress_info.get('total_items', 0)
            current_items = progress_info.get('current_items', 0)
            progress = progress_info.get('progress', 0)
            
            logger.info(f"📊 进度更新: 第{current_page}/{total_pages}页, "
                       f"当前页{current_items}个, 总计{total_items}个商品, "
                       f"进度{progress}%")
        
        # 采集数据
        products = engine.collect_search_data(
            keyword="虚拟商品",
            max_pages=2,  # 测试2页
            min_want_count=1,  # 降低门槛便于测试
            progress_callback=progress_callback
        )
        
        if products:
            logger.info(f"✅ 测试成功！采集到 {len(products)} 个商品")
            
            # 显示前3个商品的信息
            for i, product in enumerate(products[:3]):
                logger.info(f"商品 {i+1}:")
                logger.info(f"  标题: {product.get('title', 'N/A')[:50]}...")
                logger.info(f"  价格: {product.get('price', 'N/A')}")
                logger.info(f"  想要人数: {product.get('wantCount', 'N/A')}")
                logger.info(f"  链接: {product.get('link', 'N/A')}")
                logger.info("  ---")
                
        else:
            logger.warning("⚠️ 未采集到任何商品，但程序运行正常")
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        try:
            if 'engine' in locals() and engine.page:
                engine.cleanup()
                logger.info("清理完成")
        except:
            pass

if __name__ == "__main__":
    test_fixed_functionality()
